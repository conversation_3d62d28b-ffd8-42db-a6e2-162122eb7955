import * as React from "react";
import {
  Pressable,
  ActivityIndicator,
} from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { Audio } from "expo-av";

// Define an interface for the component props
interface MP3RecordingProps {
  Speech: { stop: () => void }; // Assuming Speech has a stop method
  setIsSpeaking: React.Dispatch<React.SetStateAction<any>>; // Type for the state setter
  onRecordingComplete: (uri: string) => void; // Callback function type
  autoStart?: boolean; // New prop to enable auto-start
}

export default function MP3Recording({ Speech, setIsSpeaking, onRecordingComplete, autoStart = false }: MP3RecordingProps) {
  const [isRecording, setIsRecording] = React.useState<boolean>(false); // Renamed for clarity
  const [transcribing, setTranscribing] = React.useState<boolean>(false);
  const recordingRef = React.useRef<Audio.Recording | null>(null); // Ref to hold the recording object
  const stopTimeoutRef = React.useRef<NodeJS.Timeout | null>(null); // <-- Add this ref
  const silenceTimeoutRef = React.useRef<NodeJS.Timeout | null>(null); // For silence detection
  const lastAudioLevelRef = React.useRef<number>(0);
  const silenceStartTimeRef = React.useRef<number | null>(null);
  const hasStartedRef = React.useRef<boolean>(false); // Track if auto-start has been triggered

  // Silence detection constants
  const SILENCE_THRESHOLD = 0.01; // Threshold for detecting silence
  const SILENCE_DURATION = 3000; // 3 seconds of silence before stopping
  const AUDIO_LEVEL_CHECK_INTERVAL = 100; // Check audio level every 100ms

  // Silence detection function
  const checkForSilence = React.useCallback(async () => {
    if (!recordingRef.current) return;

    try {
      const status = await recordingRef.current.getStatusAsync();
      if (status.isRecording) {
        // Get audio level (metering data)
        const currentLevel = status.metering || 0;
        const currentTime = Date.now();

        if (currentLevel < SILENCE_THRESHOLD) {
          // Silence detected
          if (silenceStartTimeRef.current === null) {
            silenceStartTimeRef.current = currentTime;
          } else if (currentTime - silenceStartTimeRef.current >= SILENCE_DURATION) {
            // Prolonged silence detected, stop recording
            console.log("Prolonged silence detected, stopping recording");
            await stopRecording();
            return;
          }
        } else {
          // Audio detected, reset silence timer
          silenceStartTimeRef.current = null;
        }

        lastAudioLevelRef.current = currentLevel;

        // Schedule next check
        silenceTimeoutRef.current = setTimeout(checkForSilence, AUDIO_LEVEL_CHECK_INTERVAL);
      }
    } catch (error) {
      console.error("Error checking audio level:", error);
    }
  }, []);

  // Use expo-av permission handling
  async function checkAndRequestMicrophonePermission() {
    const permission = await Audio.getPermissionsAsync();
    if (permission.granted) {
      return true;
    }
    const response = await Audio.requestPermissionsAsync();
    return response.granted;
  }

  async function startRecording() {
    Speech.stop();
    setIsSpeaking(null);
    const hasPermission = await checkAndRequestMicrophonePermission();
    if (!hasPermission) {
      console.error("No microphone permission granted");
      return;
    }

    try {
      // Configure audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create recording with metering enabled for silence detection
      const recordingOptions = {
        ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
        android: {
          ...Audio.RecordingOptionsPresets.HIGH_QUALITY.android,
          extension: '.mp3',
          outputFormat: Audio.AndroidOutputFormat.MPEG_4,
          audioEncoder: Audio.AndroidAudioEncoder.AAC,
        },
        ios: {
          ...Audio.RecordingOptionsPresets.HIGH_QUALITY.ios,
          extension: '.mp3',
          outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
          audioQuality: Audio.IOSAudioQuality.HIGH,
        },
        isMeteringEnabled: true, // Enable metering for silence detection
      };

      const { recording } = await Audio.Recording.createAsync(recordingOptions);
      recordingRef.current = recording;
      setIsRecording(true);

      // Reset silence detection variables
      silenceStartTimeRef.current = null;
      lastAudioLevelRef.current = 0;

      // Start silence detection
      silenceTimeoutRef.current = setTimeout(checkForSilence, AUDIO_LEVEL_CHECK_INTERVAL);

      // Set a timeout to auto-stop after 30 seconds
      stopTimeoutRef.current = setTimeout(() => {
        stopRecording();
      }, 30000);
    } catch (error) {
      console.error("Error starting recording:", error);
      setIsRecording(false); // Reset state on error
    }
  }

  async function stopRecording() {
    if (!recordingRef.current) {
      console.log("No active recording to stop.");
      return;
    }

    // Clear all timeouts
    if (stopTimeoutRef.current) {
      clearTimeout(stopTimeoutRef.current);
      stopTimeoutRef.current = null;
    }
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }

    setTranscribing(true); // Indicate transcription process starts
    setIsRecording(false); // Update UI state

    try {
      await recordingRef.current.stopAndUnloadAsync();
      const uri = recordingRef.current.getURI(); // Get the URI of the recorded file
      recordingRef.current = null; // Clear the ref

      // Reset silence detection variables
      silenceStartTimeRef.current = null;
      lastAudioLevelRef.current = 0;

      // Reset audio mode (optional, depends on app needs)
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });

      if (uri && onRecordingComplete) {
        onRecordingComplete(uri); // Pass the URI to the callback
      }
    } catch (error) {
      console.error("Error stopping recording:", error);
    } finally {
      setTranscribing(false); // Transcription process finished (or failed)
    }
  }

  // Auto-start recording when component mounts if autoStart is true
  React.useEffect(() => {
    if (autoStart && !hasStartedRef.current && !isRecording) {
      hasStartedRef.current = true;
      // Small delay to ensure component is fully mounted
      setTimeout(() => {
        startRecording();
      }, 500);
    }
  }, [autoStart, isRecording]);

  React.useEffect(() => {
    // Cleanup function
    return () => {
      if (recordingRef.current) {
        recordingRef.current.stopAndUnloadAsync(); // Ensure recording is stopped and unloaded
        recordingRef.current = null;
      }
      // Clear all timeouts on unmount
      if (stopTimeoutRef.current) {
        clearTimeout(stopTimeoutRef.current);
        stopTimeoutRef.current = null;
      }
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
        silenceTimeoutRef.current = null;
      }
    };
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  return (
    <Pressable
      onPress={isRecording ? stopRecording : startRecording} // Use updated state variable
      style={{ height: 58, marginLeft: 10 }}
    >
      {transcribing ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <FontAwesome
          name={isRecording ? "stop-circle" : "microphone"} // Use updated state variable
          size={36}
          color="#13538f"
        />
      )}
    </Pressable>
  );
}
