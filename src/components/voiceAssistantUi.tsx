import React, { useEffect, useRef, useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import * as Speech from "expo-speech";
import MP3Recording from "./MP3Recording";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import styles from "../styles/voiceAssistantStyle";
import { handleRecordingComplete } from "./handleTranscription";

const avatarSource = require("../../assets/icons/bot.jpg");

const VoiceAssistant = ({
  transcriptionResult,
  setTranscriptionResult,
  chatId,
  isSpeaking,
  setIsSpeaking,
  setTextSubmitted,
}: any) => {
  const [shouldAutoRestart, setShouldAutoRestart] = useState(false);
  const [lastResponseLength, setLastResponseLength] = useState(0);
  const mp3RecordingRef = useRef<any>(null);
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const recordingComplete = (uri: string) => {
    handleRecordingComplete(
      transcriptionResult,
      setTranscriptionResult,
      chatId,
      uri,
      setTextSubmitted
    );
  };

  // Monitor for new LLM responses and handle speech synthesis
  useEffect(() => {
    if (transcriptionResult.length > lastResponseLength) {
      const lastItem = transcriptionResult[transcriptionResult.length - 1];

      // If we have a new response from the LLM, prepare for auto-restart
      if (lastItem?.type === "res" && lastItem.text) {
        setShouldAutoRestart(true);
        setLastResponseLength(transcriptionResult.length);

        // Start speech synthesis
        Speech.speak(lastItem.text, {
          language: "en-US",
          rate: 0.9,
          onDone: () => {
            // Speech synthesis completed, restart recording after a short delay
            speechTimeoutRef.current = setTimeout(() => {
              if (shouldAutoRestart && mp3RecordingRef.current) {
                mp3RecordingRef.current.restartRecording();
              }
            }, 1000); // 1 second delay after speech ends
          },
          onStopped: () => {
            // Speech was stopped (interrupted), don't auto-restart
            setShouldAutoRestart(false);
          }
        });
      }
    }
  }, [transcriptionResult, lastResponseLength, shouldAutoRestart]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (speechTimeoutRef.current) {
        clearTimeout(speechTimeoutRef.current);
      }
      Speech.stop();
    };
  }, []);
  return (
    <LinearGradient
      colors={["#0a1535", "#1a2a5e", "#0a1535"]}
      style={styles.container}
    >
      {/* Timer */}
      {/* <Text style={styles.timer}>{formatTime(timer)}</Text> */}

      {/* Avatar Container */}
      <View style={styles.avatarContainer}>
        {/* Main Avatar */}
        <View style={styles.avatarCircle}>
          <Image source={avatarSource} style={styles.avatar} />
        </View>

        {/* Speaking Indicator */}
        {/* {isSpeaking && (
          <View style={styles.speakingBubble}>
            <View style={styles.wavesContainer}>
              <View style={[styles.wave, styles.wave1]} />
              <View style={[styles.wave, styles.wave2]} />
              <View style={[styles.wave, styles.wave3]} />
            </View>
          </View>
        )} */}

        {/* User Speaking Indicator */}
        {/* {isSpeaking && (
          <View style={styles.userSpeakingBubble}>
            <View style={styles.userWavesContainer}>
              <View style={[styles.userWave, styles.userWave1]} />
              <View style={[styles.userWave, styles.userWave2]} />
              <View style={[styles.userWave, styles.userWave3]} />
            </View>
          </View>
        )} */}
      </View>

      {/* Response Text */}
      <View style={styles.textContainer}>
        {transcriptionResult.length > 0 &&
        <Text style={styles.responseText}>
          {transcriptionResult[transcriptionResult.length - 1].text}
        </Text>
        }
      </View>

      {/* Tap to interrupt */}
      {/* <TouchableOpacity
        style={styles.interruptArea}
        onPress={() => {
          if (isSpeaking) {
            // handlePauseResume();
          }
        }}
        activeOpacity={0.7}
      >
        <Text style={styles.interruptText}>
          {isSpeaking ? "Tap to interrupt" : ""}
        </Text>
      </TouchableOpacity> */}

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        {/* <TouchableOpacity
          style={styles.controlButton}
          onPress={handlePauseResume}
        >
          {isPaused ? (
            <Ionicons name="play" size={30} color="#fff" />
          ) : (
            <Ionicons name="pause" size={30} color="#fff" />
          )}
        </TouchableOpacity> */}

        {/* Record Button using MP3Recording */}
        <View style={{ alignItems: "center" }}>
          <MP3Recording
            ref={mp3RecordingRef}
            setIsSpeaking={setIsSpeaking}
            Speech={Speech}
            onRecordingComplete={recordingComplete}
            autoStart={true}
          />
        </View>

        {/* <TouchableOpacity
          style={[styles.controlButton, styles.primaryButton]}
          onPress={handleEndCall}
        >
          <MaterialIcons name="call-end" size={30} color="#fff" />
        </TouchableOpacity> */}

        <TouchableOpacity style={styles.controlButton}>
          <Ionicons name="settings-outline" size={30} color="#fff" />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
};

export default VoiceAssistant;
