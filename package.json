{"name": "speakup", "version": "1.1.1", "scripts": {"start": "npx expo start --dev-client", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "eject": "npx expo eject", "postinstall": "patch-package", "test": "jest"}, "dependencies": {"@armata99/react-native-selectable-text": "https://github.com/sirramin/react-native-selectable-text.git", "@cafebazaar/react-native-poolakey": "^3.1.2", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-native-google-signin/google-signin": "^11.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "axios-retry": "^4.5.0", "buffer": "^6.0.3", "expo": "^51.0.25", "expo-av": "~14.0.6", "expo-clipboard": "~6.0.3", "expo-dev-client": "~4.0.28", "expo-font": "~12.0.9", "expo-linear-gradient": "~13.0.2", "expo-speech": "~12.0.2", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "node-wav": "^0.0.2", "react": "18.2.0", "react-native": "0.74.5", "react-native-animated-numbers": "^0.6.3", "react-native-audio-record": "^0.2.2", "react-native-audio-recorder-player": "^3.6.12", "react-native-element-dropdown": "^2.12.1", "react-native-fs": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-loading-dots": "github:sirramin/react-native-loading-dots", "react-native-popover-view": "^5.1.9", "react-native-progress": "^5.0.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "^15.9.0", "react-native-toast-message": "^2.2.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/helper-remap-async-to-generator": "^7.25.9", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~18.2.79", "@types/react-native": "~0.72.2", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-test-renderer": "^18.2.0", "typescript": "~5.3.3"}, "private": true}